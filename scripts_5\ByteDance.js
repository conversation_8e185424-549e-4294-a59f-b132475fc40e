import { BaseSdk, VideoAdCode } from "./BaseSdk";
import { SdkAudioAdapter } from "./AudioAdapter";
import { GameUtil } from "./GameUtil";
import { Notifier } from "./Notifier";
import { ListenID } from "./ListenID";
import { default as ttPostbackCtl } from "./ttPostbackCtl";
import { CallID } from "./CallID";
import { Manager } from "./Manager";
import { getOpenid } from "./Api";
import { UIManager } from "./UIManager";
import { MVC } from "./MVC";
var y = window.tt;
var def_ByteDance = function (e) {
  function _ctor() {
    var t = null !== e && e.apply(this, arguments) || this;
    t._bannerAd = null;
    t._cdKey = "";
    t._showBannerNum = 0;
    t._videoAd = null;
    t.ispreLoadVideo = false;
    t.lastVideoPlayTime = 0;
    t._onPlayEnd = null;
    t.interstitialAd = null;
    t._isCanNavigateTo = false;
    t._isCanAddtoDestop = true;
    t._curscene = "";
    return t;
  }
  cc__extends(_ctor, e);
  _ctor.prototype.logout = function () { };
  _ctor.prototype.setShareList = function (e) {
    console.log("获取到分享配置", e);
    this.shareList = e;
  };
  _ctor.prototype.init = function (t) {
    var o = this;
    e.prototype.init.call(this, t);
    if (window.tt) {
      $2ttPostbackCtl.default.GetInstance().init();
      y.setKeepScreenOn({
        keepScreenOn: true
      });
      y.showShareMenu({
        success: function () {
          console.log("已成功显示转发按钮");
        },
        fail: function (e) {
          console.log("showShareMenu 调用失败", e.errMsg);
        },
        complete: function () {
          console.log("showShareMenu 调用完成");
        }
      });
      y.onShareAppMessage(function (e) {
        if (e && "video" == e.channel) {
          return {
            channel: "video",
            title: "最强小兵",
            imageUrl: "",
            extra: {
              withVideoId: true
            },
            success: function (e) {
              console.warn("分享成功", e);
            },
            fail: function (e) {
              console.warn("分享失败", e);
            }
          };
        } else {
          return {
            title: "最强小兵",
            desc: $2GameUtil.GameUtil.weightGetValue(o.shareList, "weight"),
            success: function () { },
            fail: function () { }
          };
        }
      });
      if (y.getUpdateManager) {
        var i = y.getUpdateManager();
        i.onCheckForUpdate(function (e) {
          e.hasUpdate && y.showToast({
            title: "即将有更新请留意"
          });
        });
        i.onUpdateReady(function () {
          y.showModal({
            title: "更新提示",
            content: "新版本已经准备好，是否立即使用？",
            success: function (e) {
              if (e.confirm) {
                i.applyUpdate();
              } else {
                y.showToast({
                  icon: "none",
                  title: "小程序下一次「冷启动」时会使用新版本"
                });
              }
            }
          });
        });
        i.onUpdateFailed(function () { });
      }
      $2Notifier.Notifier.changeListener(true, $2ListenID.ListenID.Platform_NavigateTo, this.navigateToScene, this);
      $2Notifier.Notifier.changeListener(true, $2ListenID.ListenID.Platform_AddtoDestop, this.addtodestop, this);
      $2Notifier.Notifier.changeListener(true, $2ListenID.ListenID.Login_Finish, this.onLogin_Finish, this);
      $2Notifier.Notifier.changeListener(true, $2ListenID.ListenID.ByteDance_Check_Gift, this.checkGiftCode, this);
      $2Notifier.Notifier.changeCall(true, $2CallID.CallID.Platform_CanNavigateTo, this.isCanNavigateTo, this);
      $2Notifier.Notifier.changeCall(true, $2CallID.CallID.Platform_CanAddDestop, this.isCanAddDestop, this);
      $2Notifier.Notifier.changeCall(true, $2CallID.CallID.Platform_GetScene, this.getScene, this);
      $2Notifier.Notifier.changeCall(true, $2CallID.CallID.Platform_Query, this.getQuery, this);
      $2Notifier.Notifier.changeCall(true, $2CallID.CallID.Platform_CdKey, this.getCdKey, this);
      var n = null == y ? undefined : y.getLaunchOptionsSync();
      console.log("[getLaunchOptionsSync]", n);
      y.onShow(function (e) {
        var t;
        console.log("进入前台onshow", e);
        o.checkSceneValue(null == e ? undefined : e.scene);
        o.checkGiftCode(null === (t = null == e ? undefined : e.query) || undefined === t ? undefined : t.game_cdkey);
      });
    }
  };
  _ctor.prototype.getCdKey = function () {
    var e;
    if ("" == this._cdKey || null == this._cdKey || null == this._cdKey) {
      if (null === (e = this.getQuery()) || undefined === e) {
        return undefined;
      } else {
        return e.game_cdkey;
      }
    } else {
      return this._cdKey;
    }
  };
  _ctor.prototype.checkGiftCode = function (e, t) {
    this._cdKey = e;
    console.log("checkGiftCode gameCdKey", this._cdKey);
    if ("" != this._cdKey && null != this._cdKey && null != this._cdKey) {
      var o = cc.sys.localStorage.getItem("received_gift_code_list", []);
      var i = [];
      o && "" != o && (i = (i = JSON.parse(o)) || []);
      if (this._cdKey && !i.includes(this._cdKey)) {
        if (t) {
          $2UIManager.UIManager.OpenInQueue("ui/setting/ExchangeCodeView", $2MVC.MVC.openArgs().setParam({
            isLive: true
          }));
        } else {
          $2Notifier.Notifier.send($2ListenID.ListenID.Activity_OpenExchangeCode, true);
        }
      }
    }
  };
  _ctor.prototype.onLogin_Finish = function () {
    var e = this;
    console.log("[ByteDance][onLogin_Finish]");
    if (!(null == y ? undefined : y.checkScene)) {
      return console.log("缺少[checkScene]方法");
    }
    null == y || y.checkScene({
      scene: "sidebar",
      success: function (t) {
        console.log("[checkScene]success", t);
        e._isCanNavigateTo = null == t ? undefined : t.isExist;
        $2Notifier.Notifier.send($2ListenID.ListenID.Main_ResetView);
      },
      complete: function (e) {
        console.log("[checkScene]complete", e);
      },
      fail: function (e) {
        console.log("[checkScene]fail", e);
      }
    });
    "android" == y.getSystemInfoSync().platform && y.checkShortcut({
      success: function (e) {
        console.log("检查快捷方式", e.status);
        e.status.exist && !$2Manager.Manager.vo.knapsackVo.has("TTScenc_destop") && $2Manager.Manager.vo.knapsackVo.addGoods("TTScenc_destop");
      },
      fail: function (e) {
        console.log("检查快捷方式失败", e.errMsg);
      }
    });
  };
  _ctor.prototype.getQuery = function () {
    var e;
    if (null === (e = y.getLaunchOptionsSync()) || undefined === e) {
      return undefined;
    } else {
      return e.query;
    }
  };
  _ctor.prototype.login = function (e, t) {
    return new Promise(function (o, i) {
      y.login({
        force: true,
        success: function (n) {
          console.log("## login: ", n);
          console.log("## app_name: " + wonderSdk.BMS_APP_NAME);
          $2Api.getOpenid({
            code: n.code,
            app_name: wonderSdk.BMS_APP_NAME
          }, function (n) {
            if (0 == n.data.code) {
              console.log("## 获取openid成功 ：", n, n.data.data.openid);
              $2Manager.Manager.vo.openId = n.data.data.openid;
              o(true);
              e && e(null);
            } else {
              console.warn("## 获取openid失败，返回参数为：", n.data);
              i(false);
              t && t(null);
            }
          }, function (e) {
            console.warn("## 调用获取openid接口失败：", e);
            i(false);
            t && t(null);
          });
        }
      });
    });
  };
  _ctor.prototype.showBannerWithNode = function (e, t, o) {
    this.showBannerWithStyle(e, {}, o);
  };
  _ctor.prototype.realNameAuth = function () {
    y.authenticateRealName && y.authenticateRealName({
      success: function () {
        console.log("用户实名认证成功");
      },
      fail: function (e) {
        console.log("用户实名认证失败", e.errMsg);
      }
    });
  };
  _ctor.prototype.showBannerWithStyle = function (e, t, o) {
    var i = this;
    if (this._isByteDancePlatform && y.createBannerAd) {
      var n = y.getSystemInfoSync();
      var r = n.windowWidth;
      var a = n.windowHeight;
      this._showBannerNum++;
      if (this._bannerAd) {
        this._showBannerNum <= 1 && this._bannerAd.show().then(function () {
          console.log("广告展示成功");
        }).catch(function (e) {
          console.error("广告组件出现问题", e);
          i._showBannerNum--;
          i._showBannerNum < 0 && (i._showBannerNum = 0);
        });
      } else {
        this._bannerAd = y.createBannerAd({
          adUnitId: e,
          adIntervals: 30,
          style: {
            width: 208,
            top: a - 117
          }
        });
        this._bannerAd.style.left = (r - 208) / 2;
        this._bannerAd.onResize(function (e) {
          i._bannerAd.style.top = a - e.height;
          i._bannerAd.style.left = (r - e.width) / 2;
        });
        this._bannerAd.onLoad(function () {
          o && o();
          i._showBannerNum > 0 && i._bannerAd.show().then(function () {
            console.log("广告展示成功");
          }).catch(function (e) {
            console.error("广告组件出现问题", e);
            i._showBannerNum--;
            i._showBannerNum < 0 && (i._showBannerNum = 0);
          });
        });
      }
    } else {
      console.log("not support");
    }
  };
  _ctor.prototype.hideBanner = function () {
    this._showBannerNum--;
    if (this._showBannerNum <= 0) {
      this._showBannerNum = 0;
      this._bannerAd && this._bannerAd.hide();
    }
  };
  _ctor.prototype.destroyBanner = function () {
    if (this._bannerAd) {
      this._bannerAd.destroy();
      this._bannerAd = null;
    }
  };
  _ctor.prototype.preLoadRewardVideo = function () { };
  _ctor.prototype.showVideoAD = function (e, t) {
    var o = this;
    this._onPlayEnd = null;
    this._onPlayEnd = t;
    if (wonderSdk.isTest) {
      this._onPlayEnd && this._onPlayEnd($2BaseSdk.VideoAdCode.COMPLETE, "");
    } else if (this._isByteDancePlatform && y.createRewardedVideoAd) {
      if (Date.now() - this.lastVideoPlayTime < 1e3) {
        this._onPlayEnd && this._onPlayEnd($2BaseSdk.VideoAdCode.NOT_READY, "视频广告还在准备中，请稍后尝试");
      } else {
        if (!this._videoAd) {
          // $2ttPostbackCtl.default.GetInstance().adRequest("激励视频");
          this._videoAd = y.createRewardedVideoAd({
            adUnitId: e
          });
          this._videoAd.onLoad(function () {
            // $2ttPostbackCtl.default.GetInstance().adFill("激励视频");
          });
        }
        var i = function (e) {
          o._videoAd.offClose(i);
          $2AudioAdapter.SdkAudioAdapter.resumeMusic();
          o.ispreLoadVideo = false;
          if (e && e.isEnded || undefined === e) {
            o._onPlayEnd && o._onPlayEnd($2BaseSdk.VideoAdCode.COMPLETE, "");
            // o._onPlayEnd && $2ttPostbackCtl.default.GetInstance().adImpressionDone("激励视频");
          } else {
            o._onPlayEnd && o._onPlayEnd($2BaseSdk.VideoAdCode.NOT_COMPLITE, "未完整观看视频广告");
          }
          o._onPlayEnd = null;
        };
        this._videoAd.onClose(i);
        this._videoAd.onError(function () {
          o._onPlayEnd && o._onPlayEnd($2BaseSdk.VideoAdCode.AD_ERROR, "广告出现错误，请稍后重试");
        });
        $2AudioAdapter.SdkAudioAdapter.pauseMusic();
        // $2ttPostbackCtl.default.GetInstance().adClick("激励视频");
        this._videoAd.show().then(function () {
          // $2ttPostbackCtl.default.GetInstance().adImpression("激励视频");
        }).catch(function (e) {
          console.log("广告组件出现问题，手动load一次", e);
          o._videoAd.load().then(function () {
            o._videoAd.show().cathc(function () {
              $2AudioAdapter.SdkAudioAdapter.resumeMusic();
              o.ispreLoadVideo = false;
              o._onPlayEnd && o._onPlayEnd($2BaseSdk.VideoAdCode.AD_ERROR, "视频显示异常，请稍后再试！");
            });
          }).catch(function () {
            $2AudioAdapter.SdkAudioAdapter.resumeMusic();
            o.ispreLoadVideo = false;
            o._onPlayEnd && o._onPlayEnd($2BaseSdk.VideoAdCode.AD_ERROR, "视频加载异常，请稍后再试！");
          });
        });
      }
    } else {
      this._onPlayEnd && this._onPlayEnd($2BaseSdk.VideoAdCode.NOT_SUPPORT, "不支持视频广告, 请更新头条版本");
    }
  };
  _ctor.prototype.sendEvent = function (e, t) {
    "reward_btn" == e && cc.sys.getNetworkType() == cc.sys.NetworkType.NONE || $2Notifier.Notifier.send($2ListenID.ListenID.Event_SendEvent, e, t);
  };
  _ctor.prototype.share = function (e, t, o, i) {
    if (t) {
      var n = $2GameUtil.GameUtil.weightGetValue(this.shareList, "weight");
      t.extra || (t.extra = {});
      t.extra.withVideoId = !t.extra.withVideoId || t.extra.withVideoId;
      t.extra.hashtag_list = t.extra.hashtag_list ? t.extra.hashtag_list : ["割草", "肉鸽", "挑战", "休闲"];
      var r = {
        channel: t.channel || "article",
        extra: t.extra,
        title: n.title,
        desc: n.title,
        imageUrl: n.image,
        success: function () {
          o && o();
        },
        fail: function (e) {
          i && i(e);
        }
      };
      y.shareAppMessage(r);
    }
  };
  _ctor.prototype.showFullVideoAD = function () { };
  _ctor.prototype.showInsertAd = function (e) {
    if (y.createInterstitialAd) {
      if (this.interstitialAd) {
        this.interstitialAd && this.interstitialAd.load();
      } else {
        var t = y.createInterstitialAd({
          adUnitId: e
        });
        this.interstitialAd = t;
        t.onLoad(function () {
          t.show().then(function () {
            console.log("插屏广告展示成功");
          });
        });
        this.interstitialAd.onError(function (e) {
          console.log("插屏发生错误", e.errCode, e.errMsg);
        });
      }
    }
  };
  _ctor.prototype.destroyInsertAd = function () {
    if (this.interstitialAd) {
      this.interstitialAd.destroy();
      this.interstitialAd = null;
    }
  };
  Object.defineProperty(_ctor.prototype, "_isByteDancePlatform", {
    get: function () {
      return undefined !== y;
    },
    enumerable: false,
    configurable: true
  });
  _ctor.prototype.isCanNavigateTo = function () {
    return this._isCanNavigateTo;
  };
  _ctor.prototype.isCanAddDestop = function () {
    return this._isCanAddtoDestop;
  };
  _ctor.prototype.vibrate = function (e) {
    undefined === e && (e = 0);
    if (0 == e) {
      y.vibrateShort({
        success: function () { },
        fail: function () { }
      });
    } else {
      y.vibrateLong({
        success: function () { },
        fail: function () { }
      });
    }
  };
  _ctor.prototype.navigateToScene = function (e) {
    undefined === e && (e = "sidebar");
    y.navigateToScene({
      scene: e
    });
  };
  _ctor.prototype.getScene = function () {
    return this._curscene;
  };
  _ctor.prototype.checkSceneValue = function (e) {
    e && (this._curscene = e);
  };
  _ctor.prototype.addtodestop = function () {
    y.addShortcut({
      success: function () {
        console.log("添加桌面成功");
        $2Manager.Manager.vo.knapsackVo.addGoods("TTScenc_destop");
        $2Notifier.Notifier.send($2ListenID.ListenID.Main_ResetView);
      },
      fail: function (e) {
        console.log("添加桌面失败", e.errMsg);
      }
    });
  };
  _ctor.prototype.getStorageItem = function (e) {
    return y.getStorageSync(e);
  };
  _ctor.prototype.setStorageItem = function (e, t) {
    y.setStorageSync(e, t);
  };
  _ctor.prototype.clearStorage = function () {
    y.clearStorage({
      complete: function (e) {
        console.log("clearStorage", e);
      }
    });
  };
  _ctor.prototype.removeStorageItem = function (e) {
    y.removeStorage({
      key: e,
      complete: function (e) {
        console.log("removeStorage", e);
      }
    });
  };
  return _ctor;
}($2BaseSdk.BaseSdk);
exports.default = def_ByteDance;