var i;
var cc__extends = __extends;
Object.defineProperty(exports, "__esModule", {
  value: true
});
var $2MVC = require("MVC");
var $2GameUtil = require("GameUtil");
var $2Game = require("Game");
var $2RewardEvent = require("RewardEvent");
var def_BuffModel = function (e) {
  function _ctor() {
    var o = e.call(this) || this;
    o.historyBuffs = [];
    o.curbufflists = [];
    o.isfivecheck = false;
    if (null == _ctor._instance) {
      _ctor._instance = o;
      o.changeListener(true);
    }
    return o;
  }
  cc__extends(_ctor, e);
  Object.defineProperty(_ctor, "instance", {
    get: function () {
      null == _ctor._instance && (_ctor._instance = new _ctor());
      return _ctor._instance;
    },
    enumerable: false,
    configurable: true
  });
  _ctor.prototype.reset = function () {};
  Object.defineProperty(_ctor.prototype, "mode", {
    get: function () {
      return _ctor.instance;
    },
    enumerable: false,
    configurable: true
  });
  Object.defineProperty(_ctor.prototype, "game", {
    get: function () {
      return $2Game.Game.Mgr.instance;
    },
    enumerable: false,
    configurable: true
  });
  Object.defineProperty(_ctor.prototype, "role", {
    get: function () {
      var e;
      if (null === (e = this.game) || undefined === e) {
        return undefined;
      } else {
        return e.mainRole;
      }
    },
    enumerable: false,
    configurable: true
  });
  _ctor.prototype.changeListener = function () {};
  _ctor.prototype.getBuffPool = function (e, t, o, i) {
    undefined === e && (e = 1);
    undefined === t && (t = 1);
    undefined === o && (o = $2RewardEvent.RewardEvent.Type.SkillBuff);
    undefined === i && (i = true);
    var n = $2Game.ModeCfg.Skiilpool.get(this.game.bronMonsterMgr.level);
    var r = [];
    var a = [];
    var l = [];
    var u = [];
    var p = this.role;
    if (!p) {
      return null;
    }
    if (o == $2RewardEvent.RewardEvent.Type.SkillBuff) {
      n.norPassPool.forEach(function (e) {
        p.buffMgr.isHasID([e]) || r.push(e);
      });
      n.advPassPool.forEach(function (e) {
        p.buffMgr.isHasID([e]) || a.push(e);
      });
    } else if (o == $2RewardEvent.RewardEvent.Type.Buff) {
      n.norBuffPool.forEach(function (e) {
        p.buffMgr.isHasID([e]) || r.push(e);
      }), n.advBuffPool.forEach(function (e) {
        p.buffMgr.isHasID([e]) || a.push(e);
      });
    }
    u = i ? this.addWeight(r, t) : r;
    l = i ? this.addWeight(a, e) : a;
    var f = [];
    u.forEach(function (e) {
      return f.push({
        id: e,
        isAd: 0
      });
    });
    l.forEach(function (e) {
      return f.push({
        id: e,
        isAd: 1
      });
    });
    return f;
  };
  _ctor.prototype.addWeight = function (e, t) {
    undefined === t && (t = 999);
    if (0 == t) {
      return [];
    }
    var o;
    var i = [];
    for (var n = 0; n < e.length; n++) {
      var r = e[n];
      var c = 100;
      if (o = $2Game.ModeCfg.Buff.get(r)) {
        if (3 == o.type) {
          if (o.skillId) {
            for (var l = 0; l < o.skillId.length; l++) {
              if (this.role.skillMgr.hasMainID(o.skillId[l])) {
                c += 80;
              } else {
                c -= 80;
              }
            }
          } else {
            c += 100;
          }
        }
        i.push({
          id: r,
          w: c
        });
      }
    }
    e.length = 0;
    for (n = i.length - 1; n >= 0; n--) {
      var u = $2GameUtil.GameUtil.weightGetValue(i);
      var p = i.indexOf(u);
      e.push(u.id);
      i.splice(p, 1);
      if (e.length >= t) {
        return e;
      }
    }
    return e;
  };
  _ctor.prototype.excludeBuff = function (e) {
    var t = this;
    this.isfivecheck || this.curbufflists.forEach(function (o) {
      -1 == e.indexOf(o) && t.historyBuffs.push(o);
    });
  };
  _ctor._instance = null;
  return _ctor;
}($2MVC.MVC.BaseModel);
exports.default = def_BuffModel;